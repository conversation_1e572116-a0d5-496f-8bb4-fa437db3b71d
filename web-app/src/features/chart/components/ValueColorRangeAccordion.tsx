import { useState } from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Typography,
  Alert,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddIcon from '@mui/icons-material/Add';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import { ControllerRenderProps } from 'react-hook-form';
import { ValueColorRange, ChartConfigurationFormInput } from '../chartTypes';
import { validateColorRanges, generateAutoColorRanges, randomThemeColor } from '../chartFunctions';
import { StatsKeyValue } from '../../stats/statsTypes';
import ValueColorRangePicker from './ValueColorRangePicker';

interface ValueColorRangeAccordionProps {
  field: ControllerRenderProps<ChartConfigurationFormInput, 'valueColorRanges'>;
  expanded: boolean;
  disabled?: boolean;
  setExpanded: (expanded: boolean) => void;
  values?: number[];
  statsData?: StatsKeyValue;
  isLoading?: boolean;
}

function ValueColorRangeAccordion({
  field,
  disabled = false,
  expanded,
  setExpanded,
  values,
  statsData,
  isLoading = false,
}: ValueColorRangeAccordionProps) {
  const [validationError, setValidationError] = useState<string | null>(null);

  const ranges = field.value || [];
  const maxValue = values ? Math.max(...values) : 0;

  const handleAddRange = () => {
    const newRange: ValueColorRange = {
      minValue: ranges.length > 0 ? Math.max(...ranges.map((r) => r.maxValue)) : 0,
      maxValue,
      color: randomThemeColor(),
    };

    const updatedRanges = [...ranges, newRange];
    field.onChange(updatedRanges);

    // Validate after adding
    const error = validateColorRanges(updatedRanges);
    setValidationError(error);
  };

  const handleRangeChange = (index: number, updatedRange: ValueColorRange) => {
    const updatedRanges = ranges.map((range, i) => (i === index ? updatedRange : range));
    field.onChange(updatedRanges);

    // Validate after change
    const error = validateColorRanges(updatedRanges);
    setValidationError(error);
  };

  const handleDeleteRange = (index: number) => {
    const updatedRanges = ranges.filter((_, i) => i !== index);
    field.onChange(updatedRanges);

    // Validate after deletion
    const error = validateColorRanges(updatedRanges);
    setValidationError(error);
  };

  const handleAutoGenerate = () => {
    if (!values) {
      setValidationError('No data available for auto-generation');
      return;
    }

    try {
      if (values.length === 0) {
        setValidationError('No numerical values found in the data');
        return;
      }

      const autoRanges = generateAutoColorRanges(values, statsData);
      field.onChange(autoRanges);

      // Validate the generated ranges
      const error = validateColorRanges(autoRanges);
      setValidationError(error);
    } catch (err) {
      setValidationError(`Failed to generate ranges: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  return (
    <Accordion
      expanded={expanded}
      onChange={(_, isExpanded) => setExpanded(isExpanded)}
      elevation={0}
      sx={{
        '&:before': {
          display: 'none',
        },
        '&.Mui-expanded': {
          margin: '0',
        },
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        disabled={isLoading || disabled}
        sx={{
          padding: 0,
          '& .MuiAccordionSummary-content': {
            margin: 0,
          },
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="subtitle2">
            Value-based colors
            {ranges.length > 0 && (
              <Typography component="span" variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
                ({ranges.length} range{ranges.length !== 1 ? 's' : ''})
              </Typography>
            )}
          </Typography>
        </Box>
      </AccordionSummary>
      <AccordionDetails sx={{ padding: 0 }}>
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <CircularProgress size={24} />
          </Box>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Define color ranges based on cell values. Each range maps values to specific colors.
            </Typography>

            {validationError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {validationError}
              </Alert>
            )}

            {ranges.map((range, index) => (
              <ValueColorRangePicker
                key={`range-${range.minValue}-${range.maxValue}-${range.color}`}
                range={range}
                index={index}
                onChange={handleRangeChange}
                onDelete={handleDeleteRange}
              />
            ))}

            <Box sx={{ display: 'flex', gap: 1, mt: 1, alignItems: 'flex-start' }}>
              <Button disabled={disabled} startIcon={<AddIcon />} onClick={handleAddRange} variant="outlined" size="small">
                Add Range
              </Button>

              <Tooltip title="Generate evenly-spaced color ranges with clean threshold values as a starting template">
                <Button
                  startIcon={<AutoFixHighIcon />}
                  onClick={handleAutoGenerate}
                  variant="outlined"
                  size="small"
                  disabled={!values || isLoading || disabled}
                  color="secondary"
                >
                  Auto-generate ranges
                </Button>
              </Tooltip>
            </Box>

            {ranges.length === 0 && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
                No color ranges defined. The heatmap will use default colors.
              </Typography>
            )}
          </Box>
        )}
      </AccordionDetails>
    </Accordion>
  );
}

export default ValueColorRangeAccordion;
