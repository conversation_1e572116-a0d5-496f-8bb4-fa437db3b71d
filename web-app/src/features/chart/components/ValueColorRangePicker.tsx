import { useEffect, useState } from 'react';
import { Box, TextField, IconButton, Select, MenuItem } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { themeToColor } from '../../../theme';
import { paletteColors, ValueColorRange } from '../chartTypes';

interface ValueColorRangePickerProps {
  range: ValueColorRange;
  index: number;
  onChange: (index: number, range: ValueColorRange) => void;
  onDelete: (index: number) => void;
  error?: string;
}

function ValueColorRangePicker({ range, index, onChange, onDelete, error }: ValueColorRangePickerProps) {
  const [minValueText, setMinValueText] = useState(range.minValue.toString());
  const [maxValueText, setMaxValueText] = useState(range.maxValue.toString());

  useEffect(() => {
    setMinValueText(range.minValue.toString());
    setMaxValueText(range.maxValue.toString());
  }, [range.minValue, range.maxValue]);

  const handleMinValueChange = (value: string) => {
    setMinValueText(value);
    const numValue = parseFloat(value);
    if (!Number.isNaN(numValue)) {
      onChange(index, { ...range, minValue: numValue });
    }
  };

  const handleMaxValueChange = (value: string) => {
    setMaxValueText(value);
    const numValue = parseFloat(value);
    if (!Number.isNaN(numValue)) {
      onChange(index, { ...range, maxValue: numValue });
    }
  };

  const handleColorChange = (color: string) => {
    onChange(index, { ...range, color });
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        mb: 1,
        p: 1,
        border: error ? '1px solid #f44336' : '1px solid #e0e0e0',
        borderRadius: 1,
        backgroundColor: error ? '#ffeaea' : 'transparent',
      }}
    >
      <TextField
        label="Min"
        type="number"
        size="small"
        value={minValueText}
        onChange={(e) => handleMinValueChange(e.target.value)}
        sx={{ width: 80 }}
        inputProps={{ step: 1 }}
      />

      <Box sx={{ mx: 1, color: 'text.secondary' }}>to</Box>

      <TextField
        label="Max"
        type="number"
        size="small"
        value={maxValueText}
        onChange={(e) => handleMaxValueChange(e.target.value)}
        sx={{ width: 80 }}
        inputProps={{ step: 1 }}
      />

      <Select
        size="small"
        value={range.color}
        onChange={(e) => handleColorChange(e.target.value as string)}
        sx={{
          minWidth: 100,
          height: 32,
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
            px: 1,
          },
        }}
        renderValue={(selected) => (
          <Box
            sx={{
              width: '100%',
              height: 14,
              borderRadius: 1,
              bgcolor: selected,
            }}
          />
        )}
      >
        {paletteColors.map((color) => (
          <MenuItem key={color} value={themeToColor(color)} sx={{ py: 1, px: 1 }}>
            <Box
              sx={{
                width: '100%',
                height: 14,
                borderRadius: 1,
                bgcolor: color,
              }}
            />
          </MenuItem>
        ))}
      </Select>

      <IconButton size="small" onClick={() => onDelete(index)} sx={{ color: 'error.main' }}>
        <DeleteIcon fontSize="small" />
      </IconButton>

      {error && <Box sx={{ color: 'error.main', fontSize: '0.75rem', ml: 1 }}>{error}</Box>}
    </Box>
  );
}

export default ValueColorRangePicker;
